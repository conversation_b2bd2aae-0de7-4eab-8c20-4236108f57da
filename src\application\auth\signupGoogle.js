import * as authService from '@/services/auth.service';
import { useUserStore } from '@/stores/user/userStore';
import getUserInfo from '../user/getUserInfo';
import { updateUserDataLayer, trackSignup } from '@/helpers/analytics';

const signupGoogleUseCase = async ({
  access_token,
  rewardful_referral,
  contact_consent,
  kb_id,
  isLanding,
  trial_plan_type = 'pro'
}) => {
  const saveAuthDetail = useUserStore.getState().saveAuthDetail;
  const setUser = useUserStore.getState().setUser;

  const response = await authService.signupWithGoogle({
    access_token,
    rewardful_referral,
    contact_consent,
    kb_id,
    isLanding,
    trial_plan_type
  });
  let userInfo;

  const res = response.data;

  if (response.status === 200) {
    const auth = {};
    auth.access_token = res.access_token;
    saveAuthDetail(auth);

    userInfo = await getUserInfo(res.access_token);

    auth.user_id = userInfo.id;
    auth.first_name = userInfo.first_name;
    auth.last_name = userInfo.last_name;
    auth.email = userInfo.email;
    auth.date_created = userInfo.date_created;
    auth.login_count = userInfo.login_count;

    saveAuthDetail(auth);
    setUser(userInfo);

    // Track signup event
    trackSignup({
      user_id: userInfo.id,
      email: userInfo.email,
      first_name: userInfo.first_name || '',
      last_name: userInfo.last_name || '',
      tier_name: 'pro',
      billing_cycle: 'none',
      signup_method: 'Google'
    });

    // Update dataLayer with user information
    updateUserDataLayer(userInfo, auth);

    return { status: 200, data: { ...res, ...userInfo } };
  }

  return {};
};

export default signupGoogleUseCase;
